<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { page } from "$app/stores"
  import { <PERSON><PERSON>, <PERSON>, Users, Wrench, Plus, TrendingUp, Clock, Star, Grid3X3, List } from "lucide-svelte"

  export let selectedSection: string | null = null
  export let selectedAgent: string | null = null
  export let selectedTool: any = null
  export let selectedCategory: string | null = null
  export let allTools: any[] = []
  export let filteredTools: any[] = []
  export let categoryStats: any = {}
  export let leftPanelOpen: boolean = true
  export let rightPanelOpen: boolean = false
  export let dashboardData: any = {}
  export let session: any
  export let environment: any

  const dispatch = createEventDispatcher()

  // View mode state
  let agentsViewMode: 'grid' | 'list' = 'grid'
  let toolsViewMode: 'grid' | 'list' = 'grid'

  function toggleLeftPanel() {
    dispatch('toggleLeftPanel')
  }

  function toggleRightPanel() {
    dispatch('toggleRightPanel')
  }

  function toggleAgentsViewMode() {
    agentsViewMode = agentsViewMode === 'grid' ? 'list' : 'grid'
  }

  function toggleToolsViewMode() {
    toolsViewMode = toolsViewMode === 'grid' ? 'list' : 'grid'
  }

  // Agent definitions (matching the nav panel)
  const agents = [
    {
      id: 'campaign-orchestrator',
      name: 'Deep Researcher',
      description: 'Coordinate and automate multi-channel marketing campaigns with AI-driven orchestration.',
      features: ['Multi-channel campaign planning', 'Automated content generation', 'Timeline & budget optimization'],
      route: `/dashboard/${$page.params.envSlug}/campaign-orchestrator`,
      color: 'bg-blue-500'
    },
    {
      id: 'agent-seo',
      name: 'SEO Agent',
      description: 'Get comprehensive keyword research and SEO strategy recommendations for your business.',
      features: ['Keyword analysis & research', 'Competitive SEO insights', 'Content strategy recommendations'],
      route: `/dashboard/${$page.params.envSlug}/agent-seo`,
      color: 'bg-green-500'
    },
    {
      id: 'content-agent',
      name: 'Content Agent',
      description: 'Create, edit, and optimize content with AI assistance. Generate outlines, improve writing, and manage citations.',
      features: ['Content generation & outlines', 'Grammar & style correction', 'Citation management'],
      route: `/dashboard/${$page.params.envSlug}/content-agent`,
      color: 'bg-purple-500'
    }
  ]

  function handleAgentClick(agent: any) {
    window.location.href = agent.route
  }

  function handleToolClick(tool: any) {
    dispatch('toolClick', tool)
  }

  function getContentTitle() {
    if (selectedSection === 'content-board') return 'Content Board'
    if (selectedSection === 'agents') return 'Marketing Agents'
    if (selectedSection === 'tools') return 'Available Tools'
    return 'Dashboard'
  }

  function getContentDescription() {
    if (selectedSection === 'content-board') return 'Your central knowledge repository and content management hub'
    if (selectedSection === 'agents') return 'AI-powered assistants for your marketing workflows'
    if (selectedSection === 'tools') {
      if (selectedCategory) {
        return `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} tools that power our AI agents`
      }
      return 'Specialized utilities that power our AI agents'
    }
    return 'Your augmented marketing team. Powered by agents.'
  }

  // Filter tools by category when selectedCategory is set
  $: displayTools = selectedCategory
    ? allTools.filter(tool => tool.category === selectedCategory)
    : filteredTools
</script>

<div class="flex flex-col h-full bg-background">
  <!-- Header -->
  <div class="flex items-center justify-between p-6 border-b border-border bg-card">
    <div class="flex items-center gap-4">
      {#if !leftPanelOpen}
        <button
          on:click={toggleLeftPanel}
          class="p-2 hover:bg-muted rounded-lg transition-colors"
          title="Show navigation panel"
        >
          <Menu class="w-5 h-5 text-foreground" />
        </button>
      {/if}
      
      <div>
        <h1 class="text-3xl font-bold text-foreground mb-2">{getContentTitle()}</h1>
        <p class="text-muted-foreground">{getContentDescription()}</p>
      </div>
    </div>

    <div class="flex items-center gap-2">
      <!-- View mode toggles for agents and tools -->
      {#if selectedSection === 'agents' || selectedSection === 'tools'}
        <div class="flex border border-border bg-background">
          <button
            on:click={selectedSection === 'agents' ? toggleAgentsViewMode : toggleToolsViewMode}
            class="px-3 py-2 text-sm font-medium transition-colors {(selectedSection === 'agents' ? agentsViewMode : toolsViewMode) === 'grid'
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'}"
          >
            <Grid3X3 class="h-4 w-4" />
          </button>
          <button
            on:click={selectedSection === 'agents' ? toggleAgentsViewMode : toggleToolsViewMode}
            class="px-3 py-2 text-sm font-medium transition-colors border-l border-border {(selectedSection === 'agents' ? agentsViewMode : toolsViewMode) === 'list'
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'}"
          >
            <List class="h-4 w-4" />
          </button>
        </div>
      {/if}

      <button
        on:click={toggleRightPanel}
        class="flex items-center gap-2 px-3 py-1.5 text-sm border border-border bg-background hover:bg-accent transition-colors rounded-lg"
      >
        {#if rightPanelOpen}
          Close Insights
        {:else}
          Show Insights
        {/if}
      </button>
    </div>
  </div>

  <!-- Content Area -->
  <div class="flex-1 overflow-y-auto p-6">
    {#if selectedSection === 'content-board'}
      <!-- Content Board Overview -->
      <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="card-professional p-6">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg flex items-center justify-center">
                <Brain class="w-5 h-5" />
              </div>
              <div>
                <h3 class="font-semibold text-foreground">Knowledge Base</h3>
                <p class="text-sm text-muted-foreground">Central repository</p>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">Documents</span>
                <span class="font-medium">24</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">External Sources</span>
                <span class="font-medium">3</span>
              </div>
            </div>
            <button
              on:click={() => window.location.href = `/dashboard/${$page.params.envSlug}/content-board`}
              class="w-full mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
            >
              Open Content Board
            </button>
          </div>

          <div class="card-professional p-6">
            <div class="flex items-center gap-3 mb-4">
              <Clock class="w-5 h-5 text-blue-500" />
              <div>
                <h3 class="font-semibold text-foreground">Recent Activity</h3>
                <p class="text-sm text-muted-foreground">Last 7 days</p>
              </div>
            </div>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-muted-foreground">Document created</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span class="text-muted-foreground">External source connected</span>
              </div>
            </div>
          </div>

          <div class="card-professional p-6">
            <div class="flex items-center gap-3 mb-4">
              <TrendingUp class="w-5 h-5 text-green-500" />
              <div>
                <h3 class="font-semibold text-foreground">Usage Stats</h3>
                <p class="text-sm text-muted-foreground">This month</p>
              </div>
            </div>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-muted-foreground">Content created</span>
                <span class="font-medium">12 docs</span>
              </div>
              <div class="flex justify-between">
                <span class="text-muted-foreground">AI assistance</span>
                <span class="font-medium">48 requests</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    {:else if selectedSection === 'agents'}
      <!-- Agents Overview -->
      <div class="space-y-6">
        {#if agentsViewMode === 'grid'}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {#each agents as agent}
              <div class="card-professional p-6 transition-all duration-200 hover-transform">
                <div class="space-y-4">
                  <div class="flex items-center gap-3">
                    <div class="w-12 h-12 {agent.color} text-white border border-border shadow-soft-sm flex items-center justify-center">
                      <Users class="h-6 w-6" />
                    </div>
                    <div>
                      <h3 class="text-xl font-semibold text-foreground">{agent.name}</h3>
                      <div class="flex items-center gap-2 mt-1">
                        <span class="bg-accent text-accent-foreground px-2 py-1 text-xs font-bold border border-border">
                          AI-Powered
                        </span>
                      </div>
                    </div>
                  </div>
                  <p class="text-muted-foreground font-medium">{agent.description}</p>
                  <div class="text-sm text-muted-foreground space-y-1">
                    {#each agent.features as feature}
                      <p>• {feature}</p>
                    {/each}
                  </div>
                  <button
                    on:click={() => handleAgentClick(agent)}
                    class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
                  >
                    Open {agent.name}
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <!-- List View -->
          <div class="space-y-4">
            {#each agents as agent}
              <div class="card-professional p-4 transition-all duration-200 hover-transform">
                <div class="flex items-center gap-4">
                  <div class="w-10 h-10 {agent.color} text-white border border-border shadow-soft-sm flex items-center justify-center flex-shrink-0">
                    <Users class="h-5 w-5" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-foreground">{agent.name}</h3>
                    <p class="text-sm text-muted-foreground truncate">{agent.description}</p>
                  </div>
                  <button
                    on:click={() => handleAgentClick(agent)}
                    class="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
                  >
                    Open
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>

    {:else if selectedSection === 'tools'}
      <!-- Tools Overview -->
      <div class="space-y-6">
        <p class="text-muted-foreground">
          Explore the powerful tools that power our AI agents. Each tool specializes in specific tasks to deliver exceptional results.
        </p>

        {#if toolsViewMode === 'grid'}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {#each displayTools.slice(0, 12) as tool}
              <button
                on:click={() => handleToolClick(tool)}
                class="card-professional p-4 text-left transition-all duration-200 hover-transform"
              >
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 bg-muted text-muted-foreground rounded-lg flex items-center justify-center flex-shrink-0">
                    <Wrench class="w-4 h-4" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-foreground text-sm mb-1">{tool.name}</h3>
                    <p class="text-xs text-muted-foreground line-clamp-2">{tool.description}</p>
                    <span class="inline-block mt-2 px-2 py-0.5 text-xs bg-muted text-muted-foreground rounded-full">
                      {tool.category}
                    </span>
                  </div>
                </div>
              </button>
            {/each}
          </div>
        {:else}
          <!-- List View -->
          <div class="space-y-2">
            {#each displayTools.slice(0, 20) as tool}
              <button
                on:click={() => handleToolClick(tool)}
                class="w-full card-professional p-3 text-left transition-all duration-200 hover-transform"
              >
                <div class="flex items-center gap-3">
                  <div class="w-6 h-6 bg-muted text-muted-foreground rounded flex items-center justify-center flex-shrink-0">
                    <Wrench class="w-3 h-3" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2">
                      <h3 class="font-medium text-foreground text-sm">{tool.name}</h3>
                      <span class="px-2 py-0.5 text-xs bg-muted text-muted-foreground rounded-full">
                        {tool.category}
                      </span>
                    </div>
                    <p class="text-xs text-muted-foreground truncate">{tool.description}</p>
                  </div>
                </div>
              </button>
            {/each}
          </div>
        {/if}
      </div>

    {:else}
      <!-- Default Dashboard Overview -->
      <div class="space-y-8">
        <!-- Welcome Section -->
        <div class="section-muted card-professional p-8">
          <div class="text-center">
            <h1 class="text-3xl font-semibold text-foreground mb-4">
              Welcome to{" "}
              <span class="gradient-text-primary"> Robynn.ai </span>
            </h1>
            <p class="text-lg text-muted-foreground mb-6 font-medium">
              Your augmented marketing team. Powered by agents.
            </p>
          </div>
        </div>

        <!-- Quick Access Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button
            on:click={() => dispatch('sectionSelect', { section: 'content-board' })}
            class="card-professional p-6 text-left transition-all duration-200 hover-transform"
          >
            <div class="flex items-center gap-3 mb-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg flex items-center justify-center">
                <Brain class="w-6 h-6" />
              </div>
              <div>
                <h3 class="font-semibold text-foreground">Content Board</h3>
                <p class="text-sm text-muted-foreground">Knowledge Foundation</p>
              </div>
            </div>
            <p class="text-sm text-muted-foreground">
              Manage your content and connect external sources
            </p>
          </button>

          <button
            on:click={() => dispatch('sectionSelect', { section: 'agents' })}
            class="card-professional p-6 text-left transition-all duration-200 hover-transform"
          >
            <div class="flex items-center gap-3 mb-4">
              <div class="w-12 h-12 bg-secondary text-secondary-foreground rounded-lg flex items-center justify-center">
                <Users class="w-6 h-6" />
              </div>
              <div>
                <h3 class="font-semibold text-foreground">AI Agents</h3>
                <p class="text-sm text-muted-foreground">Intelligent Assistants</p>
              </div>
            </div>
            <p class="text-sm text-muted-foreground">
              Access your AI-powered marketing team
            </p>
          </button>

          <button
            on:click={() => dispatch('sectionSelect', { section: 'tools' })}
            class="card-professional p-6 text-left transition-all duration-200 hover-transform"
          >
            <div class="flex items-center gap-3 mb-4">
              <div class="w-12 h-12 bg-muted text-muted-foreground rounded-lg flex items-center justify-center">
                <Wrench class="w-6 h-6" />
              </div>
              <div>
                <h3 class="font-semibold text-foreground">Tools</h3>
                <p class="text-sm text-muted-foreground">Supporting Utilities</p>
              </div>
            </div>
            <p class="text-sm text-muted-foreground">
              Explore the tools that power our agents
            </p>
          </button>
        </div>
      </div>
    {/if}
  </div>
</div>

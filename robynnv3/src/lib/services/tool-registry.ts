import type { Agent } from '@mastra/core';

export type ToolCategory = 'content' | 'research' | 'seo' | 'apollo' | 'firecrawl';

export interface ToolInfo {
  id: string;
  name: string;
  description: string;
  category: ToolCategory;
  icon: string;
  usedBy: AgentInfo[];
  status: 'active' | 'beta' | 'deprecated';
  inputSchema?: any;
}

export interface AgentInfo {
  id: string;
  name: string;
  href: string;
}

export class ToolRegistry {
  private static toolMetadata: Record<string, Omit<ToolInfo, 'id' | 'usedBy' | 'inputSchema'>> = {
    'content-generation': {
      name: 'Content Generator',
      description: 'Create high-quality content for various formats and audiences',
      category: 'content',
      icon: '✍️',
      status: 'active'
    },
    'text-summarization': {
      name: 'Text Summarizer',
      description: 'Create concise, focused summaries of text content in various formats',
      category: 'content',
      icon: '📝',
      status: 'active'
    },
    'grammar-style-correction': {
      name: 'Grammar & Style Checker',
      description: 'Check and correct grammar, style, tone, and readability of text',
      category: 'content',
      icon: '✏️',
      status: 'active'
    },
    'outline-generation': {
      name: 'Outline Generator',
      description: 'Generate structured, hierarchical outlines for various content types',
      category: 'content',
      icon: '📋',
      status: 'active'
    },
    'citation-management': {
      name: 'Citation Manager',
      description: 'Generate properly formatted citations in various academic styles',
      category: 'content',
      icon: '📚',
      status: 'active'
    },
    'web-search': {
      name: 'Web Search',
      description: 'Search the web for comprehensive company and research information',
      category: 'research',
      icon: '🔍',
      status: 'active'
    },
    'exa-search-enhanced': {
      name: 'Enhanced Web Search',
      description: 'Advanced web search with competitor intelligence and company research',
      category: 'research',
      icon: '🔎',
      status: 'active'
    },
    'get_keyword_volume': {
      name: 'Keyword Volume',
      description: 'Get monthly search volume data for keywords using DataForSEO',
      category: 'seo',
      icon: '📊',
      status: 'active'
    },
    'get_keyword_difficulty': {
      name: 'Keyword Difficulty',
      description: 'Get SEO difficulty scores (0-100) for keywords',
      category: 'seo',
      icon: '📈',
      status: 'active'
    },
    'get_related_keywords': {
      name: 'Related Keywords',
      description: 'Discover related and long-tail keywords based on seed keywords',
      category: 'seo',
      icon: '🔗',
      status: 'active'
    },
    'get_domain_intersection': {
      name: 'Domain Intersection',
      description: 'Find keywords where two domains both rank in Google SERPs',
      category: 'seo',
      icon: '⚡',
      status: 'active'
    },
    'get_keywords_for_site': {
      name: 'Keywords for Site',
      description: 'Get all keywords a website or webpage ranks for',
      category: 'seo',
      icon: '🎯',
      status: 'active'
    },
    'apollo_search_company': {
      name: 'Apollo Company Search',
      description: 'Enrich target company data using Apollo API by domain or name',
      category: 'apollo',
      icon: '🏢',
      status: 'active'
    },
    'apollo_find_companies': {
      name: 'Apollo Company Discovery',
      description: 'Discover matching companies based on attributes using Apollo API',
      category: 'apollo',
      icon: '🔍',
      status: 'active'
    },
    'apollo_find_contacts': {
      name: 'Apollo Contact Finder',
      description: 'Retrieve contacts for companies using Apollo API',
      category: 'apollo',
      icon: '👥',
      status: 'active'
    },
    'firecrawl-scrape': {
      name: 'Firecrawl Scraper',
      description: 'Deep single-page analysis with AI-powered content extraction',
      category: 'firecrawl',
      icon: '🔍',
      status: 'active'
    },
    'firecrawl-extract': {
      name: 'Firecrawl Extractor',
      description: 'Bulk data extraction from multiple URLs with schema-driven extraction',
      category: 'firecrawl',
      icon: '📊',
      status: 'active'
    },
    'firecrawl-search': {
      name: 'Firecrawl Search',
      description: 'Web search with automatic result scraping and content extraction',
      category: 'firecrawl',
      icon: '🌐',
      status: 'active'
    }
  };

  private static agentToolMapping: Record<string, string[]> = {
    'content-agent': [
      'content-generation',
      'text-summarization', 
      'grammar-style-correction',
      'outline-generation',
      'citation-management',
      'web-search'
    ],
    'seo-strategist': [
      'web-search',
      'get_keyword_volume',
      'get_keyword_difficulty', 
      'get_related_keywords',
      'get_domain_intersection',
      'get_keywords_for_site'
    ],
    'orchestrator-agent': [
      'apollo_search_company',
      'apollo_find_companies',
      'apollo_find_contacts',
      'exa-search-enhanced',
      'firecrawl-scrape',
      'firecrawl-extract',
      'firecrawl-search'
    ],
    'company-researcher': [
      'web-search'
    ]
  };

  private static agentInfo: Record<string, AgentInfo> = {
    'content-agent': {
      id: 'content-agent',
      name: 'Content Agent',
      href: '/dashboard/production/agents/content'
    },
    'seo-strategist': {
      id: 'seo-strategist', 
      name: 'SEO Strategist',
      href: '/dashboard/production/agents/seo'
    },
    'orchestrator-agent': {
      id: 'orchestrator-agent',
      name: 'Deep Researcher Agent',
      href: '/dashboard/production/agents/orchestrator'
    },
    'company-researcher': {
      id: 'company-researcher',
      name: 'Company Researcher',
      href: '/dashboard/production/agents/researcher'
    }
  };

  static getAllTools(): ToolInfo[] {
    const tools: ToolInfo[] = [];
    
    for (const [toolId, metadata] of Object.entries(this.toolMetadata)) {
      const usedBy: AgentInfo[] = [];
      
      // Find which agents use this tool
      for (const [agentId, toolIds] of Object.entries(this.agentToolMapping)) {
        if (toolIds.includes(toolId)) {
          const agentInfo = this.agentInfo[agentId];
          if (agentInfo) {
            usedBy.push(agentInfo);
          }
        }
      }
      
      tools.push({
        id: toolId,
        ...metadata,
        usedBy
      });
    }
    
    return tools.sort((a, b) => a.name.localeCompare(b.name));
  }

  static getToolsByCategory(category: ToolCategory): ToolInfo[] {
    return this.getAllTools().filter(tool => tool.category === category);
  }

  static getToolById(id: string): ToolInfo | null {
    const metadata = this.toolMetadata[id];
    if (!metadata) return null;

    const usedBy: AgentInfo[] = [];
    for (const [agentId, toolIds] of Object.entries(this.agentToolMapping)) {
      if (toolIds.includes(id)) {
        const agentInfo = this.agentInfo[agentId];
        if (agentInfo) {
          usedBy.push(agentInfo);
        }
      }
    }

    return {
      id,
      ...metadata,
      usedBy
    };
  }

  static getToolsByAgent(agentId: string): ToolInfo[] {
    const toolIds = this.agentToolMapping[agentId] || [];
    return toolIds.map(id => this.getToolById(id)).filter(Boolean) as ToolInfo[];
  }

  static getCategoryStats(): Record<ToolCategory, number> {
    const stats: Record<ToolCategory, number> = {
      content: 0,
      research: 0,
      seo: 0,
      apollo: 0
    };

    for (const metadata of Object.values(this.toolMetadata)) {
      stats[metadata.category]++;
    }

    return stats;
  }
}

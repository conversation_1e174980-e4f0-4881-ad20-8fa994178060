<script lang="ts">
  import { fly } from "svelte/transition"
  import {
    Menu,
    ChevronLeft,
    ChevronRight,
    Settings,
    LogOut,
  } from "lucide-svelte"

  import { buttonVariants } from "$lib/components/ui/button"
  import * as Dialog from "$lib/components/ui/dialog"
  import { page } from "$app/stores"
  import { getEnvironmentState } from "$lib/states"
  import SharedFooter from "$lib/components/SharedFooter.svelte"

  let { data, children } = $props()

  let { session } = data

  let open = $state(false)
  let collapsed = $state(true)

  // Check if current page is the main dashboard (hide sidebar for dashboard)
  const isDashboardPage = $derived($page.route.id === '/(admin)/dashboard/[envSlug]/(menu)')

  // Load collapsed state from localStorage
  $effect(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("sidebar-collapsed")
      if (saved !== null) {
        collapsed = saved === "true"
      }
    }
  })

  // Save collapsed state to localStorage
  $effect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("sidebar-collapsed", String(collapsed))
    }
  })

  const basePath = "/dashboard/"

  const environment = getEnvironmentState()

  class NavItem {
    href: string
    label: string
    active: boolean

    constructor(
      href: string,
      label: string,
      isActive: (href: string) => boolean,
    ) {
      this.href = href
      this.label = label
      this.active = isActive(this.href)
    }
  }

  let navItems = $state<NavItem[]>([])

  $effect(() => {
    navItems = [
      new NavItem(
        `${basePath}${environment.value?.slug}`,
        "Home",
        (href) => $page.url.pathname === href,
      ),
    ]
  })

  let settingsItem = $state<NavItem>()

  $effect(() => {
    settingsItem = new NavItem(
      `${basePath}${environment.value?.slug}/settings`,
      "⚙️",
      (href) => $page.url.pathname.startsWith(href),
    )
  })
</script>

<div class="min-h-screen relative">
  <div
    class="{isDashboardPage ? 'absolute inset-0' : 'grid grid-rows-[auto_1fr] lg:grid-rows-1 overflow-hidden absolute inset-0'}"
    style="{isDashboardPage ? '' : `grid-template-columns: ${collapsed ? '4.5rem' : '18rem'} 1fr`}"
  >
    {#if !isDashboardPage}
    <nav
      class="w-full h-16 flex items-center justify-between lg:block lg:h-dvh p-4 bg-sidebar border-r-2 border-sidebar-border text-sidebar-foreground transition-all duration-300"
      class:lg:w-[4.5rem]={collapsed}
      class:lg:w-72={!collapsed}
    >
      <div class="flex items-center space-x-2 lg:hidden">
        <div
          class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-soft-sm"
        >
          <span class="font-bold text-sm">R</span>
        </div>
        <a href="/" class="text-lg font-black">Robynn.ai</a>
      </div>
      <Dialog.Root bind:open>
        <Dialog.Trigger class="lg:hidden"
          ><button
            aria-label="open navigation"
            class="p-2 text-sidebar-foreground hover:text-sidebar-primary"
          >
            <Menu class="h-5 w-5" />
          </button></Dialog.Trigger
        >
        <Dialog.Content
          transition={(node) => fly(node, { x: 300, duration: 300 })}
          class="left-auto right-0 flex h-dvh max-h-screen w-full max-w-sm translate-x-1 flex-col overflow-y-scroll border-y-0 sm:rounded-none bg-sidebar"
        >
          <div class="p-4 border-b-2 border-sidebar-border">
            <div class="flex items-center space-x-2">
              <div
                class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-soft-sm"
              >
                <span class="font-bold text-sm">R</span>
              </div>
              <a href="/" class="text-lg font-black text-sidebar-foreground"
                >Robynn.ai</a
              >
            </div>
          </div>
          <ul class="flex flex-col p-4 space-y-1">
            {#each navItems as { href, label, active }}
              <li>
                <a
                  {href}
                  class="block w-full px-3 py-2 text-sm font-bold transition-colors border-2 {active
                    ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-soft-sm'
                    : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
                  onclick={() => (open = false)}
                >
                  {label}
                </a>
              </li>
            {/each}
            <div class="flex-grow"></div>
            <li class="pt-4 border-t-2 border-sidebar-border">
              <div class="flex items-center justify-between px-3 py-2">
                {#if settingsItem}
                  <a
                    href={settingsItem.href}
                    class="text-sm font-bold transition-colors border-2 p-1 rounded {settingsItem.active
                      ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-soft-sm'
                      : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
                    onclick={() => (open = false)}
                  >
                    {settingsItem.label}
                  </a>
                {/if}
                <a
                  href="/dashboard/{environment.value?.slug}/../../sign_out"
                  class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors"
                  onclick={() => (open = false)}
                >
                  Sign Out
                </a>
              </div>
            </li>
          </ul>
        </Dialog.Content>
      </Dialog.Root>
      <ul class="hidden flex-col h-full lg:flex">
        <li class="mb-8">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div
                class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-soft-sm"
              >
                <span class="font-bold text-sm">R</span>
              </div>
              {#if !collapsed}
                <a href="/" class="text-lg font-black text-sidebar-foreground"
                  >Robynn.ai</a
                >
              {/if}
            </div>
            <button
              onclick={() => (collapsed = !collapsed)}
              class="p-1.5 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors rounded border-2 border-transparent hover:border-sidebar-border"
              aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {#if collapsed}
                <ChevronRight class="h-4 w-4" />
              {:else}
                <ChevronLeft class="h-4 w-4" />
              {/if}
            </button>
          </div>
        </li>

        <nav class="space-y-1">
          {#each navItems as item}
            <a
              href={item.href}
              class="block px-3 py-2 text-sm font-bold transition-colors border-2 {item.active
                ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-soft-sm'
                : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
              title={collapsed ? item.label : undefined}
            >
              {#if collapsed}
                <span class="block text-center">🏠</span>
              {:else}
                {item.label}
              {/if}
            </a>
          {/each}
        </nav>

        <div class="flex-grow"></div>
        <div class="border-t-2 border-sidebar-border pt-4">
          <div
            class="flex items-center gap-2 px-3 py-2"
            class:justify-center={collapsed}
          >
            {#if settingsItem}
              <a
                href={settingsItem.href}
                class="text-sm font-bold transition-colors border-2 p-1.5 rounded {settingsItem.active
                  ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-soft-sm'
                  : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
                title={collapsed ? "Settings" : undefined}
              >
                {#if collapsed}
                  <Settings class="h-4 w-4" />
                {:else}
                  {settingsItem.label}
                {/if}
              </a>
            {/if}
            <a
              href="/dashboard/{environment.value?.slug}/../../sign_out"
              class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent p-1.5 rounded hover:border-sidebar-border"
              title={collapsed ? "Sign Out" : undefined}
            >
              {#if collapsed}
                <LogOut class="h-4 w-4" />
              {:else}
                Sign Out
              {/if}
            </a>
          </div>
        </div>
      </ul>
    </nav>
    {/if}

    <div
      class="{isDashboardPage ? 'absolute inset-0 bg-background flex flex-col' : 'px-6 lg:px-12 py-6 overflow-y-scroll relative bg-background min-h-full flex flex-col'}"
    >
      {#if isDashboardPage}
        <!-- Dashboard page gets full screen -->
        {@render children()}
      {:else}
        <!-- Other pages get the normal layout -->
        <div class="flex-1">
          {#if session?.user.is_anonymous}
            <div
              class="text-sm bg-primary text-primary-foreground sticky px-4 py-3 text-center border-2 border-border shadow-soft mb-6 font-bold"
            >
              You're signed in as an anonymous user. <a
                href="/login/sign_up"
                class="underline font-bold hover:opacity-70"
                >Sign Up to persist your changes</a
              >
            </div>
          {/if}

          {@render children()}
        </div>

        <div class="mt-auto">
          <SharedFooter />
        </div>
      {/if}
    </div>
  </div>
</div>

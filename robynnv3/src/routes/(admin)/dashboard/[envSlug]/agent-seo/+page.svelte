<script lang="ts">
  import { page } from "$app/stores"
  import {
    Menu,
    ChevronRight,
    PanelRightClose,
    PanelRightOpen
  } from "lucide-svelte"

  // Import new components
  import SEOAgentSidebar from "$lib/components/seo/SEOAgentSidebar.svelte"
  import SEOCanvas from "$lib/components/seo/SEOCanvas.svelte"

  // Import stores and services
  import {
    isLoading,
    progressSteps,
    addMessage,
    handleRequestError,
    initializeChatProgressSteps,
    messages
  } from "$lib/stores/seo-agent-store"

  import {
    SEOAgentService
  } from "$lib/services/seo-agent-service"

  import { writable } from 'svelte/store'

  // Sidebar state management
  const leftSidebarCollapsed = writable(false)
  const rightSidebarCollapsed = writable(false)

  // Reactive variables
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)
  let mobileMenuOpen = $state(false)
  let sidebarOpen = $state(true)
  let seoService: SEOAgentService
  let sidebarComponent: SEOAgentSidebar

  // Error handling state
  let requestTimeout: NodeJS.Timeout | undefined
  const REQUEST_TIMEOUT_MS = 125000 // 125 seconds timeout (slightly longer than server timeout)

  // Partial results state
  let partialResultsReceived = $state(false)
  let fallbackResultsReceived = $state(false)
  let lastPartialResponse = $state("")

  // Toggle functions
  function toggleSidebar() {
    sidebarOpen = !sidebarOpen
    rightSidebarCollapsed.set(!sidebarOpen)
  }

  // Initialize service
  $effect(() => {
    if ($page.params.envSlug) {
      seoService = new SEOAgentService($page.params.envSlug)
    }
  })

  // Responsive behavior - auto-collapse on mobile
  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed.set(true)
      rightSidebarCollapsed.set(true)
    }
  })

  // Event handlers
  async function handleSendMessage(event: CustomEvent) {
    const { message } = event.detail

    if (!message.trim() || $isLoading) return

    isLoading.set(true)

    // Set up request timeout
    requestTimeout = setTimeout(() => {
      if ($isLoading) {
        handleRequestError(new Error("Request timeout"), 'timeout')
        isLoading.set(false)
      }
    }, REQUEST_TIMEOUT_MS)

    // Initialize progress steps
    initializeChatProgressSteps()

    // Add user message
    addMessage({
      role: 'user',
      content: message
    })

    try {
      const response = await seoService.sendChatMessage(
        message,
        'summary',
        {},
        (step, status, action, _progress) => {
          // Update progress steps based on server updates
          progressSteps.update(steps =>
            steps.map(stepItem => {
              if (stepItem.id < step) {
                return { ...stepItem, status: 'completed' }
              } else if (stepItem.id === step) {
                return {
                  ...stepItem,
                  status: status === 'active' ? 'active' : stepItem.status,
                  description: action || stepItem.description
                }
              }
              return stepItem
            })
          )
        },
        (error) => {
          console.error('SEO Agent error:', error)
          // Error will be handled in the catch block
        }
      )

      // Clear timeout on successful completion
      if (requestTimeout) {
        clearTimeout(requestTimeout)
        requestTimeout = undefined
      }

      // Parse metadata if present
      const metadataPattern = /<!-- Analysis Metadata: (.*?) -->/
      const metadataMatch = response.match(metadataPattern)
      let metadata = null
      let isPartialResult = false

      if (metadataMatch) {
        try {
          metadata = JSON.parse(metadataMatch[1])
          console.log("📊 Analysis metadata:", metadata)
          // Only mark as partial if metadata explicitly indicates it
          isPartialResult = metadata?.isPartial || false
        } catch (e) {
          console.error("Error parsing metadata:", e)
          // If metadata parsing fails, don't assume it's partial
          isPartialResult = false
        }
      }

      // Parse and add assistant response
      const structuredData = SEOAgentService.parseStructuredResponse(response)
      console.log("🔍 Parsed structured data:", structuredData)
      console.log("🔍 Content strategy data:", structuredData.contentStrategy)

      // Clean response for display (remove metadata and XML tags)
      let cleanedResponse = response
      console.log("🧹 Original response length:", response.length)
      console.log("🧹 Original response preview:", response.substring(0, 200))

      cleanedResponse = cleanedResponse.replace(/<!-- Analysis Metadata:.*? -->\n?/gs, "")
      cleanedResponse = cleanedResponse.replace(/<GAP_RESULTS>.*?<\/GAP_RESULTS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<KEYWORDS>.*?<\/KEYWORDS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<NICHE_KEYWORDS>.*?<\/NICHE_KEYWORDS>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<COMPANY_PROFILE>.*?<\/COMPANY_PROFILE>/gs, "")
      cleanedResponse = cleanedResponse.replace(/<CONTENT_STRATEGY>.*?<\/CONTENT_STRATEGY>/gs, "")

      console.log("🧹 Cleaned response length:", cleanedResponse.length)
      console.log("🧹 Cleaned response preview:", cleanedResponse.substring(0, 200))
      console.log("🧹 Cleaned response trimmed length:", cleanedResponse.trim().length)

      // Store partial response for potential retry
      if (isPartialResult || metadata?.isFallback) {
        lastPartialResponse = response
        partialResultsReceived = isPartialResult
        fallbackResultsReceived = metadata?.isFallback || false
      }

      // Add assistant message with structured data and metadata
      const messageToAdd = {
        role: 'assistant' as const,
        content: SEOAgentService.formatContent(cleanedResponse.trim() || response),
        data: {
          ...structuredData,
          metadata: metadata
        }
      }

      console.log("🎯 Adding message to store:", messageToAdd)
      addMessage(messageToAdd)
      console.log("🎯 Message added, current messages length should be > 0")

      // Update sidebar with response
      if (sidebarComponent) {
        const contentForSidebar = cleanedResponse.trim() || response
        console.log("📋 Content for sidebar length:", contentForSidebar.length)
        console.log("📋 Content for sidebar preview:", contentForSidebar.substring(0, 100))

        const formattedContent = SEOAgentService.formatContent(contentForSidebar)
        console.log("📋 Formatted content length:", formattedContent.length)
        console.log("📋 Formatted content preview:", formattedContent.substring(0, 100))

        await sidebarComponent.addAssistantMessage(
          formattedContent,
          structuredData
        )
      }

    } catch (error) {
      // Determine error type for better user feedback
      let errorType: 'timeout' | 'network' | 'server' | 'unknown' = 'unknown'

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('Request timeout')) {
          errorType = 'timeout'
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          errorType = 'network'
        } else if (error.message.includes('server') || error.message.includes('500')) {
          errorType = 'server'
        }
      }

      handleRequestError(error, errorType)
    } finally {
      isLoading.set(false)
      // Clear timeout if still active
      if (requestTimeout) {
        clearTimeout(requestTimeout)
        requestTimeout = undefined
      }
    }
  }



  function handleExampleSelect(event: CustomEvent) {
    const { message } = event.detail
    if (sidebarComponent) {
      sidebarComponent.populateInput(message)
    }
  }
</script>

<!-- Main Page Container -->
<div class="min-h-screen bg-background">
  <!-- Dashboard Header -->
  <header class="bg-sidebar border-b-2 border-sidebar-border text-sidebar-foreground">
    <div class="flex items-center justify-between p-4">
      <!-- Logo and Brand -->
      <div class="flex items-center space-x-2">
        <div
          class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-soft-sm"
        >
          <span class="font-bold text-sm">R</span>
        </div>
        <a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a>
      </div>

      <!-- Mobile Menu Button -->
      <button
        onclick={() => mobileMenuOpen = !mobileMenuOpen}
        class="lg:hidden p-2 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors rounded border-2 border-transparent hover:border-sidebar-border"
        aria-label="Toggle menu"
      >
        <Menu class="h-5 w-5" />
      </button>

      <!-- Desktop Navigation -->
      <div class="hidden lg:flex items-center space-x-4">
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="px-3 py-2 text-sm font-bold transition-colors border-2 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent hover:border-sidebar-border"
        >
          Dashboard
        </a>
      </div>
    </div>

    <!-- Mobile Menu -->
    {#if mobileMenuOpen}
      <div class="lg:hidden border-t border-sidebar-border bg-sidebar">
        <div class="px-4 py-2 space-y-1">
          <a
            href="/dashboard/{$page.params.envSlug}"
            class="block px-3 py-2 text-sm font-bold transition-colors border-2 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent hover:border-sidebar-border"
            onclick={() => mobileMenuOpen = false}
          >
            Dashboard
          </a>
        </div>
      </div>
    {/if}
  </header>

  <!-- Breadcrumb Navigation -->
  <div class="bg-background border-b border-border">
    <div class="px-6 py-4">
      <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">SEO Agent</span>
      </nav>
    </div>
  </div>

  <!-- Responsive Sidebar + Canvas Layout -->
  <div class="flex-1 flex bg-background relative" style="height: calc(100vh - 120px);">
    <!-- Sidebar Toggle Button -->
    <button
      onclick={toggleSidebar}
      class="sidebar-toggle-btn absolute top-4 right-4 z-40 p-2 rounded-lg border border-border bg-card hover:bg-accent transition-all duration-200 shadow-sm hover:shadow-md"
      class:toggle-with-sidebar={sidebarOpen}
      aria-label="{sidebarOpen ? 'Close SEO Agent sidebar' : 'Open SEO Agent sidebar'}"
    >
      {#if sidebarOpen}
        <PanelRightClose class="w-5 h-5 text-foreground" />
      {:else}
        <PanelRightOpen class="w-5 h-5 text-foreground" />
      {/if}
    </button>

    <!-- Main Canvas Area -->
    <SEOCanvas
      leftSidebarCollapsed={false}
      rightSidebarCollapsed={!sidebarOpen}
      messages={$messages}
      showExamples={$messages.length === 0}
      on:selectExample={handleExampleSelect}
    />

    <!-- Sidebar -->
    <SEOAgentSidebar
      bind:this={sidebarComponent}
      bind:isOpen={sidebarOpen}
      isLoading={$isLoading}
      progressSteps={$progressSteps}
      on:sendMessage={handleSendMessage}
    />
  </div>
</div>

<svelte:head>
  <title>Keystone - AI SEO Strategist</title>
</svelte:head>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />

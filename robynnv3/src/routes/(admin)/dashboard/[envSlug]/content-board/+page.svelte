<script lang="ts">
  import { onMount, onDestroy } from "svelte"
  import { page } from "$app/stores"
  import { goto } from "$app/navigation"
  import {
    PanelRightOpen,
    PanelRightClose,
    Plus,
    FileText,
    Search,
    MoreVertical,
    Trash2,
    ChevronRight,
    PenTool,
    Menu,
  } from "lucide-svelte"
  import ContentBoardLayout from "$lib/components/ContentBoardLayout.svelte"
  import { ContentService } from "$lib/services/content-service"
  import { navigationActions } from "$lib/stores/navigation"

  export let data

  let { session, supabase } = data
  let contentService = new ContentService(supabase)
  let environment: any = null

  // State
  let currentDocument: any = null
  let documents: any[] = []
  let isLoading = false
  let searchTerm = ""
  let selectedContentType = "all"
  let showDocumentList = true
  let selectedText = ""
  let canvasComponent: any
  let mobileMenuOpen = false

  // URL parameters for auto-outline mode
  let urlTopic = ""
  let urlContentType = ""
  let urlAudience = ""
  let autoGenerateOutline = false

  const contentTypes = [
    { value: "all", label: "All Types" },
    { value: "article", label: "Article" },
    { value: "blog-post", label: "Blog Post" },
    { value: "whitepaper", label: "Whitepaper" },
    { value: "social-media", label: "Social Media" },
    { value: "email", label: "Email" },
    { value: "documentation", label: "Documentation" },
    { value: "press-release", label: "Press Release" },
    { value: "case-study", label: "Case Study" },
    { value: "newsletter", label: "Newsletter" },
    { value: "landing-page", label: "Landing Page" },
  ]

  onMount(async () => {
    try {
      // Get environment data
      const { data: envData, error: envError } = await supabase
        .from("environments")
        .select("*")
        .eq("slug", $page.params.envSlug)
        .single()

      if (envError) {
        console.error("Error fetching environment:", envError)
        return
      }

      environment = envData

      // Check URL parameters for auto-outline mode
      const urlParams = new URLSearchParams(window.location.search)
      urlTopic = urlParams.get("topic") || ""
      urlContentType = urlParams.get("contentType") || ""
      urlAudience = urlParams.get("audience") || ""
      autoGenerateOutline = urlParams.has("autoOutline")

      if (autoGenerateOutline && urlTopic && urlContentType && urlAudience) {
        await createDocumentWithAutoOutline()
      } else {
        await loadDocuments()
      }
    } catch (error) {
      console.error("Error in onMount:", error)
    }
  })

  async function createDocumentWithAutoOutline() {
    try {
      isLoading = true
      showDocumentList = false

      // Create a new document
      const newDoc = await contentService.createDocument({
        title: `${urlTopic} - ${urlContentType}`,
        content: "",
        content_type: urlContentType,
        target_audience: urlAudience,
        user_id: session.user.id,
        environment_id: environment.id,
      })

      if (newDoc) {
        currentDocument = newDoc
      }
    } catch (error) {
      console.error("Error creating document with auto-outline:", error)
    } finally {
      isLoading = false
    }
  }

  async function loadDocuments() {
    try {
      isLoading = true
      documents = await contentService.getUserDocuments(
        session.user.id,
        environment.id,
        {
          limit: 50,
          contentType:
            selectedContentType === "all" ? undefined : selectedContentType,
        },
      )
    } catch (error) {
      console.error("Error loading documents:", error)
    } finally {
      isLoading = false
    }
  }

  async function createNewDocument() {
    try {
      if (!environment) {
        console.error("Environment not loaded")
        return
      }

      if (!session?.user?.id) {
        console.error("User session not available")
        return
      }

      const newDoc = await contentService.createDocument({
        title: "Untitled Document",
        content: "",
        content_type: "article",
        target_audience: "general",
        user_id: session.user.id,
        environment_id: environment.id,
      })

      if (newDoc) {
        currentDocument = newDoc
        showDocumentList = false
      }
    } catch (error) {
      console.error("Error creating document:", error)
      alert(`Failed to create document: ${error.message}`)
    }
  }

  async function openDocument(doc: any) {
    currentDocument = doc
    showDocumentList = false
  }

  async function saveDocument(event: CustomEvent) {
    if (!currentDocument) return

    try {
      const { content, title } = event.detail

      // Calculate word count and reading time
      const plainText = content
        .replace(/<[^>]*>/g, " ")
        .replace(/\s+/g, " ")
        .trim()
      const wordCount = plainText
        .split(" ")
        .filter((word) => word.length > 0).length
      const readingTime = Math.ceil(wordCount / 200) // Average reading speed

      await contentService.updateDocument(currentDocument.id, session.user.id, {
        title,
        content: content, // Save as plain text/HTML
        word_count: wordCount,
        reading_time: readingTime,
        updated_at: new Date().toISOString(),
      })

      // Update local document
      currentDocument = {
        ...currentDocument,
        title,
        content: content,
        word_count: wordCount,
        reading_time: readingTime,
      }
    } catch (error) {
      console.error("Error saving document:", error)
      throw error
    }
  }

  async function deleteDocument(docId: string) {
    if (!confirm("Are you sure you want to delete this document?")) return

    try {
      await contentService.deleteDocument(docId, session.user.id)
      documents = documents.filter((doc) => doc.id !== docId)

      // If we're currently viewing the deleted document, go back to list
      if (currentDocument?.id === docId) {
        currentDocument = null
        showDocumentList = true
      }
    } catch (error) {
      console.error("Error deleting document:", error)
      alert(`Failed to delete document: ${error.message}`)
    }
  }

  function handleSelectionChange(event: CustomEvent) {
    selectedText = event.detail.selectedText || ""
  }

  function handleContentGenerated(event: CustomEvent) {
    const { content } = event.detail

    if (currentDocument && content) {
      // Ensure content is properly formatted for HTML display
      let formattedContent = content

      // If content is plain text, convert line breaks to HTML
      if (typeof content === "string" && !content.includes("<")) {
        formattedContent = content
          .split("\n")
          .map((line) => line.trim())
          .filter((line) => line.length > 0)
          .map((line) => `<p>${line}</p>`)
          .join("")
      }

      // Update the document content with the generated content
      currentDocument.content = formattedContent

      // Force reactivity update
      currentDocument = { ...currentDocument }

      // Auto-save the generated content
      saveDocument({
        detail: {
          content: formattedContent,
          title: currentDocument.title,
        },
      } as CustomEvent)
    }
  }

  function handleReplaceContent(event: CustomEvent) {
    const { content, replaceSelected } = event.detail

    if (canvasComponent) {
      if (replaceSelected && selectedText.trim()) {
        canvasComponent.replaceSelectedContent(content)
      } else {
        canvasComponent.replaceEntireContent(content)
      }
    }
  }

  onMount(() => {
    // Content Board doesn't need persistent navigation
    // navigationActions.setAgentActive('nexus')
  })

  onDestroy(() => {
    // Clean up navigation state when component unmounts
    navigationActions.setAgentInactive()
  })

  function goBackToList() {
    currentDocument = null
    showDocumentList = true
  }

  async function searchDocuments() {
    if (!searchTerm.trim()) {
      await loadDocuments()
      return
    }

    try {
      isLoading = true
      documents = await contentService.searchDocuments(
        session.user.id,
        environment.id,
        searchTerm,
        {
          limit: 50,
          contentType:
            selectedContentType === "all" ? undefined : selectedContentType,
        },
      )
    } catch (error) {
      console.error("Error searching documents:", error)
    } finally {
      isLoading = false
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  function formatReadingTime(minutes: number) {
    if (minutes < 1) return "< 1 min read"
    return `${minutes} min read`
  }
</script>

<svelte:head>
  <title>Content Board - {environment?.name || 'Loading...'}</title>
</svelte:head>

<div class="h-screen bg-background">
  <!-- Dashboard Header -->
  <header class="bg-sidebar border-b-2 border-sidebar-border text-sidebar-foreground">
    <div class="flex items-center justify-between p-4">
      <!-- Logo and Brand -->
      <div class="flex items-center space-x-2">
        <div
          class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-soft-sm"
        >
          <span class="font-bold text-sm">R</span>
        </div>
        <a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a>
      </div>
    </div>
  </header>

  <!-- Breadcrumb Navigation -->
  <div class="bg-background border-b border-border">
    <div class="px-6 py-4">
      <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">Content Board</span>
      </nav>
    </div>
  </div>

  <ContentBoardLayout
    {currentDocument}
    {documents}
    {isLoading}
    {searchTerm}
    {selectedContentType}
    {showDocumentList}
    {selectedText}
    {contentTypes}
    {session}
    {environment}
    {contentService}
    on:createDocument={createNewDocument}
    on:openDocument={(e) => openDocument(e.detail)}
    on:saveDocument={saveDocument}
    on:deleteDocument={(e) => deleteDocument(e.detail)}
    on:selectionChange={handleSelectionChange}
    on:contentGenerated={handleContentGenerated}
    on:replaceContent={handleReplaceContent}
    on:goBackToList={goBackToList}
    on:searchDocuments={searchDocuments}
    on:loadDocuments={loadDocuments}
    bind:canvasComponent
  />
</div>

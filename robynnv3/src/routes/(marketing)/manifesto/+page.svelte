<script lang="ts">
  import { scrollY } from "$lib/stores/scroll";
  import { ArrowUp } from "lucide-svelte";
  import { fadeIn } from "$lib/animations/actions";
  import type { PageData } from "./$types";

  export let data: PageData;

  const { manifesto, meta, structuredData } = data;

  let readingProgress = 0;
  let showBackToTop = false;
  let articleElement: HTMLElement;

  // Calculate reading progress based on scroll position
  $: if (articleElement && $scrollY) {
    const articleTop = articleElement.offsetTop;
    const articleHeight = articleElement.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = $scrollY;

    if (scrollTop > articleTop) {
      const scrolledIntoArticle = scrollTop - articleTop;
      const totalScrollableHeight = articleHeight - windowHeight;
      readingProgress = Math.min(100, Math.max(0, (scrolledIntoArticle / totalScrollableHeight) * 100));
    } else {
      readingProgress = 0;
    }

    showBackToTop = scrollTop > 500;
  }

  function scrollToTop() {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }




</script>

<svelte:head>
  <title>{meta.title}</title>
  <meta name="description" content={meta.description} />
  <meta name="author" content={meta.author} />
  <meta name="keywords" content={meta.tags.join(', ')} />
  
  <!-- Open Graph -->
  <meta property="og:title" content={meta.title} />
  <meta property="og:description" content={meta.description} />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="/manifesto" />
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content={meta.title} />
  <meta name="twitter:description" content={meta.description} />
  
  <!-- Article specific -->
  <meta property="article:author" content={meta.author} />
  <meta property="article:published_time" content={meta.publishedDate} />
  <meta property="article:modified_time" content={meta.lastModified} />
  <meta property="article:section" content={meta.category} />
  {#each meta.tags as tag}
    <meta property="article:tag" content={tag} />
  {/each}

  <!-- Structured Data -->
  {#if structuredData}
    {@html `<script type="application/ld+json">${JSON.stringify(structuredData)}</script>`}
  {/if}
</svelte:head>

<!-- Reading Progress Bar -->
<div class="reading-progress">
  <div
    class="reading-progress-bar"
    style="width: {readingProgress}%"
  ></div>
</div>

<!-- Main Content -->
<div class="min-h-screen bg-background linear-grid">
  <!-- Hero Section -->
  <section class="py-16 px-6 linear-hero">
    <div class="max-w-4xl mx-auto text-center hero-content">
      {#if manifesto}
        <div use:fadeIn={{ delay: 0.2, duration: 0.8 }}>
          <h1 class="linear-heading text-4xl md:text-6xl font-bold mb-6 leading-tight">
            {manifesto.frontmatter.title}
          </h1>
          <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            {manifesto.frontmatter.subtitle}
          </p>
        </div>
      {:else}
        <!-- Fallback content -->
        <h1 class="linear-heading text-4xl md:text-6xl font-bold mb-6 leading-tight">
          The Robynn Manifesto
        </h1>
        <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
          A founder's letter to the fellow builders who refuse to accept that growth must be an assembly line
        </p>
      {/if}
    </div>
  </section>

  <!-- Article Content -->
  <article 
    bind:this={articleElement}
    class="max-w-4xl mx-auto px-6 py-16"
    use:fadeIn={{ delay: 0.8, duration: 1.0 }}
  >
    {#if manifesto}
      <!-- Author Bio -->
      <div class="mb-12 p-6 bg-card border border-border rounded-lg">
        <div class="flex items-start gap-4">
          <div class="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold">
            {manifesto.frontmatter.author.split(' ').map(n => n[0]).join('')}
          </div>
          <div>
            <h3 class="linear-heading text-lg font-semibold mb-1">{manifesto.frontmatter.author}</h3>
            <p class="text-sm text-muted-foreground mb-2">{manifesto.frontmatter.role}</p>
            <p class="linear-body text-sm">{manifesto.frontmatter.bio}</p>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="prose prose-lg max-w-none linear-body">
        {@html manifesto.content}
      </div>
    {:else}
      <div class="text-center py-16">
        <p class="linear-body text-lg text-muted-foreground">
          Content is currently unavailable. Please try again later.
        </p>
      </div>
    {/if}
  </article>
</div>

<!-- Back to Top Button -->
{#if showBackToTop}
  <button
    on:click={scrollToTop}
    class="fixed bottom-8 right-8 w-12 h-12 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40"
    aria-label="Back to top"
  >
    <ArrowUp class="w-5 h-5" />
  </button>
{/if}

<style>
  @import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap');

  .reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    z-index: 50;
  }

  .reading-progress-bar {
    height: 100%;
    background: var(--primary);
    transition: width 0.1s ease-out;
  }

  /* Merriweather typography overrides for manifesto content only */
  :global(.prose) {
    font-family: 'Merriweather', serif;
    color: var(--foreground);
    line-height: 1.7;
  }

  :global(.prose h2) {
    font-family: 'Merriweather', serif;
    font-weight: 700;
    letter-spacing: -0.01em;
    line-height: 1.2;
    font-size: 1.5rem;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    color: var(--foreground);
    scroll-margin-top: 2rem;
  }

  :global(.prose h3) {
    font-family: 'Merriweather', serif;
    font-weight: 700;
    letter-spacing: -0.01em;
    line-height: 1.2;
    font-size: 1.25rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--foreground);
    scroll-margin-top: 2rem;
  }

  :global(.prose p) {
    font-family: 'Merriweather', serif;
    font-weight: 400;
    line-height: 1.7;
    letter-spacing: 0;
    margin-bottom: 1.5rem;
    color: var(--foreground);
  }

  :global(.prose strong) {
    font-family: 'Merriweather', serif;
    font-weight: 700;
    color: var(--foreground);
  }

  :global(.prose em) {
    font-family: 'Merriweather', serif;
    font-style: italic;
  }

  :global(.prose blockquote) {
    font-family: 'Merriweather', serif;
    border-left: 4px solid var(--primary);
    padding-left: 1.5rem;
    font-style: italic;
    color: var(--muted-foreground);
    margin: 2rem 0;
    font-weight: 300;
  }

  :global(.prose ul, .prose ol) {
    font-family: 'Merriweather', serif;
    margin: 1.5rem 0;
    padding-left: 1.5rem;
  }

  :global(.prose li) {
    font-family: 'Merriweather', serif;
    margin-bottom: 0.5rem;
    line-height: 1.7;
  }

  /* Keep navigation and other elements with original fonts */
  :global(nav), :global(footer), :global(.linear-nav) {
    font-family: "Plus Jakarta Sans", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
  }

  /* Ensure hero section and metadata use original fonts */
  .hero-content, .metadata-section {
    font-family: "Plus Jakarta Sans", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    :global(.prose) {
      font-size: 1rem;
    }

    :global(.prose h2) {
      font-size: 1.5rem;
    }

    :global(.prose h3) {
      font-size: 1.25rem;
    }
  }

  /* Smooth scroll behavior */
  :global(html) {
    scroll-behavior: smooth;
  }

  /* Enhanced focus styles for accessibility */
  :global(.prose *:focus) {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: 2px;
  }
</style>

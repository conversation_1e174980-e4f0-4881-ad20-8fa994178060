<script lang="ts">
  import * as Form from "$lib/components/ui/form"
  import * as Card from "$lib/components/ui/card"
  import { superForm } from "sveltekit-superforms"
  import { zodClient } from "sveltekit-superforms/adapters"
  import { signUpSchema } from "$lib/schemas"
  import { Input } from "$lib/components/ui/input"
  import { Button } from "$lib/components/ui/button"
  import { Eye, EyeOff, AlertCircle } from "lucide-svelte"
  import { fly } from "svelte/transition"
  import {
    PasswordStrengthIndicator,
    EmailValidationFeedback,
    PasswordMatchIndicator,
  } from "$lib/components/auth"

  let { data } = $props()

  const form = superForm(data.form, {
    validators: zodClient(signUpSchema),
  })

  const { form: formData, enhance, delayed, errors, constraints } = form

  // State for password visibility and field touch tracking
  let showPassword = $state(false)
  let showConfirmPassword = $state(false)
  let emailTouched = $state(false)
  let passwordTouched = $state(false)
  let _confirmPasswordTouched = $state(false)
</script>

<svelte:head>
  <title>Sign up</title>
</svelte:head>

<Card.Root class="mt-6">
  <Card.Header>
    <Card.Title class="text-2xl font-bold text-center">Sign Up</Card.Title>
    <Card.Description
      >Create your account to get started. You'll receive a confirmation link
      via email.</Card.Description
    >
  </Card.Header>
  <Card.Content>
    <form method="post" use:enhance class="grid gap-4">
      <Form.Field {form} name="email">
        <Form.Control let:attrs>
          <Form.Label>Email</Form.Label>
          <Input
            bind:value={$formData.email}
            onblur={() => (emailTouched = true)}
            {...attrs}
            {...$constraints.email}
          />
        </Form.Control>
        <EmailValidationFeedback
          email={$formData.email}
          touched={emailTouched}
          showConfirmationNotice={true}
        />
        <Form.FieldErrors />
      </Form.Field>

      <Form.Field {form} name="password">
        <Form.Control let:attrs>
          <Form.Label>Password</Form.Label>
          <div class="relative">
            <Input
              bind:value={$formData.password}
              type={showPassword ? "text" : "password"}
              onfocus={() => (passwordTouched = true)}
              {...attrs}
              {...$constraints.password}
            />
            <button
              type="button"
              onclick={() => (showPassword = !showPassword)}
              class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors"
            >
              {#if showPassword}
                <EyeOff size={18} />
              {:else}
                <Eye size={18} />
              {/if}
            </button>
          </div>
        </Form.Control>
        <PasswordStrengthIndicator
          password={$formData.password}
          showRequirements={passwordTouched}
        />
        <Form.FieldErrors />
      </Form.Field>

      <Form.Field {form} name="confirmPassword">
        <Form.Control let:attrs>
          <Form.Label>Confirm Password</Form.Label>
          <div class="relative">
            <Input
              bind:value={$formData.confirmPassword}
              type={showConfirmPassword || showPassword ? "text" : "password"}
              onfocus={() => (_confirmPasswordTouched = true)}
              {...attrs}
              {...$constraints.confirmPassword}
            />
            <button
              type="button"
              onclick={() => (showConfirmPassword = !showConfirmPassword)}
              class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors"
            >
              {#if showConfirmPassword || showPassword}
                <EyeOff size={18} />
              {:else}
                <Eye size={18} />
              {/if}
            </button>
          </div>
        </Form.Control>
        <PasswordMatchIndicator
          password={$formData.password}
          confirmPassword={$formData.confirmPassword}
          bind:showPassword
        />
        <Form.FieldErrors />
      </Form.Field>

      {#if $errors._errors}
        <div
          class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
          transition:fly={{ y: -10, duration: 300 }}
        >
          <AlertCircle size={16} class="flex-shrink-0" />
          <span>{$errors._errors[0]}</span>
        </div>
      {/if}

      <Button type="submit" disabled={$delayed} class="w-full">
        {#if $delayed}
          ...
        {:else}
          Sign Up
        {/if}
      </Button>
    </form>
  </Card.Content>

  <Card.Footer>
    <div class="mt-4 mb-2">
      Have an account? <a class="underline" href="/login/sign_in">Sign in</a>.
    </div>
  </Card.Footer>
</Card.Root>
